plugins {
    id 'com.android.application'
    id 'kotlin-android'
    id 'kotlin-kapt'
    id 'com.autotest.opasm.CoverageInstPlugin'
    id 'kotlin-parcelize'

    id 'com.google.devtools.ksp'
}

apply from: rootProject.file("scripts/oapm.gradle")
apply from: rootProject.file("scripts/gpstore.gradle")

repositories { flatDir { dir 'libs' } }

apply plugin: 'obuildplugin'
apply from: rootProject.projectDir.path + '/config.gradle'
apply from: "../app/protobuf_config.gradle"
apply from: "../app/unitTaskEnv.gradle"

OBuildConfig {
    buildTask = "OppoFull,OppoLight,OneplusFull,OneplusLight"
    codeScanVariants = "OppoFullDomesticApilevelallDebug"
    androidTestVariants = "OppoFullDomesticApilevelallDebug"
    outputType = "aab,apk"
}
//include generic compile configs
apply from: rootProject.file('scripts/common.gradle')
android {
    namespace 'com.oplus.note'
    testNamespace 'com.coloros.note.test'
    dataBinding {
        enabled = true
    }
    sourceSets {
        main {
            aidl.srcDirs=['src/main/aidl']
        }
    }

    configurations {
        all*.exclude group: 'com.squareup.okhttp3', module: 'okhttp'
    }

    defaultConfig {
        archivesBaseName = prop_archivesBaseName
        applicationId = prop_applicationId
        manifestPlaceholders = [versionCommit: "${prop_versionCommit}", versionDate: "${prop_versionDate}", permission_key: "${prop_permission_key}", permission_key_oplus: "${oplus_prop_permission_key}"]

        javaCompileOptions {
            annotationProcessorOptions {
                arguments = [
                        "room.schemaLocation"  : "$projectDir/schemas".toString(),
                        "room.incremental"     : "false",
                        "room.expandProjection": "true"
                ]
            }
        }

        testInstrumentationRunner "com.oplus.autotest.olt.testlib.common.OplusRunner"

        ndk {
            abiFilters 'arm64-v8a'
        }
    }

    buildTypes {
        release {
            // config enable proGuard
            minifyEnabled true

            // config enable shrink unused resources
            shrinkResources true

            // proGuard rules files
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'

            buildConfigField "boolean", "ENV_DEBUG", "false"
            buildConfigField "int", "ENV_VALUE", "0"
        }

        debug {
            buildConfigField "boolean", "ENV_DEBUG", "true"
            buildConfigField "int", "ENV_VALUE", "1"
        }

        oms {
            initWith(buildTypes.release)
            matchingFallbacks = ['release']
        }
    }

    lintOptions {
        checkReleaseBuilds false
        // Or, if you prefer, you can continue to check for errors in release builds,
        // but continue the build even when errors are found:
        abortOnError false
    }

    testOptions {
        unitTests {
            includeAndroidResources = true
            returnDefaultValues = true
        }
        unitTests.all {
            jvmArgs '-noverify'
            maxHeapSize = env_maxHeapSize
            maxParallelForks = env_maxParallelForks
            reports.html.destination = file("${rootDir}/UnitTestReport")
            jacoco {
                includeNoLocationClasses = true
                excludes = ['jdk.internal.*']
            }
            systemProperty 'robolectric.dependency.repo.url', prop_oppoMavenUrl
        }
    }

    packagingOptions {
        exclude 'META-INF/TapDatabase_release.kotlin_module'
        exclude 'META-INF/core_release.kotlin_module'
        dex {
            useLegacyPackaging true
        }
    }
}

// When the build variant is OnePlus & Export,
// modify the applicationId to com.oneplus.note
// to satisfy the requirements that
// can be published on the export version of OnePlus.
android.applicationVariants.all { variant ->
    def containsOnePlus = variant.name.contains('oneplus')
    def containsExport = containsStringIgnoreCase(variant.name, 'fullexport') || containsStringIgnoreCase(variant.name, 'fullgdpr') || containsStringIgnoreCase(variant.name, 'fullindia')
    if (containsOnePlus && containsExport) {
        println("productFlavors is oneplus & export : $variant.name")
        variant.mergedFlavor.applicationId = "com.oneplus.note"
    } else {
        println("productFlavors is normal flavor : $variant.name")
        variant.mergedFlavor.applicationId = "com.coloros.note"
    }

    int versionCode = getVersionCode()
    println(" -> versionCode: $versionCode")
    if (((versionCode / 1000000) as int) % 10 != 0) {
        throw new StopExecutionException("the versionCode sixth place should 0")
    }
    variant.outputs.each { output ->
        int tversionCode = versionCode
        println("start -> tversionCode: $tversionCode")
        if (containsStringIgnoreCase(variant.name, "export")) {
            // OOS12.0 + S，印度区域，由于在region分区，项目集成的是fullindia下的包；
            // OOS12.1 + S及以上，印度区域，由于在stock分区，项目集成的是fullexport下的包。
            // fullindia的versionCode加了3000000，fullexport只加了1000000，这会导致OOS12.0 + S项目集成的Apk，实际上versionCode要比OOS12.1 + S的要高，
            // OTA升级场景，由于版本号降级，会导致覆盖安装失败。因此，修改fullexport下的versionCode高于fullindia的。
            tversionCode += 4000000
        } else if (containsStringIgnoreCase(variant.name, "gdpr")) {
            tversionCode += 2000000
        }/* else if (containsStringIgnoreCase(variant.name, "fullindia")) {
            tversionCode += 3000000
        }*/
        println("end -> tversionCode: $tversionCode")
        output.versionCodeOverride = tversionCode
        variant.mergedFlavor.manifestPlaceholders.put("versioncode", tversionCode)
    }
}

private static def containsStringIgnoreCase(parent, child) {
    parent.toLowerCase().contains(child)
}

// CloudKit依赖TapHttp，TapHttp是基于OkHttp定制的，需要去除OkHttp依赖
configurations {
    compile.exclude group: "com.squareup.okhttp3", module: "okhttp"
}
configurations.all {
    exclude group: 'com.coui.support', module: 'coui-support-appcompat'
}
apply from: "deps-cloudkit.gradle"
apply from: rootProject.projectDir.path + "/coui_uikit.gradle"
//apply from: rootProject.projectDir.path + "/scripts/oms/isExportAndBundle.gradle"
//if (ext.domesticOms.call()) {
//    apply from: rootProject.projectDir.path + "/scripts/oms/oms_app.gradle"
//}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.aar'])
    implementation project(":richtext:core")
    implementation project(":richtext:editor")
    implementation project(":richtext:transform")
    implementation project(":feature:scenecard")
    implementation project(":feature:notebook")
    implementation project(':data:todo-repository')
    implementation project(':data:note-repository')

    implementation project(":common:logger:logger-api")
    implementation project(':common:logger:logger-dcs')
    domesticImplementation project(":common:logger:logger-feedback")
    implementation project(':common:baseres')

    implementation project(path: ':common:osdk-proxy')
    implementation project(path: ':common:lib_api')
    implementation project(path: ':common:lib_base')
    implementation project(path: ':common:permission')
    implementation project(path: ':common:baseres')
    implementation project(path: ':common:audioplayer')
    //implementation project(path: ':sdk-office')

    implementation project(':library-sync:cloud-sync-support')
//    implementation project(path: ':sdk-office')
    implementation project(path: ':webview:container-api')

    implementation project(path: ':webviewcoverpaint:container-impl')
    implementation project(path: ':webviewcoverpaint:container-api')
/*    implementation project(path: ':webviewcoverpaint')*/
    implementation project(path: ':webview:container-impl')

    // tbl webview
    implementation "com.oplus.tbl.webview:tbl-webview-sdk:${tbl_webview_sdk_version}"
    implementation 'com.heytap.msp.sdk:msp-kit-sync-sdk:1.0.0.5'

    oppoFullDomesticApilevelallImplementation project(":library-sync:coloros-sync-support")
    oppoLightDomesticApilevelallImplementation project(":library-sync:coloros-sync-support")
    oneplusFullDomesticApilevelallImplementation project(":library-sync:coloros-sync-support")
    oneplusLightDomesticApilevelallImplementation project(":library-sync:coloros-sync-support")
    oppoFullExportApilevelallImplementation project(":library-sync:oplus-sync-support")
    oppoLightExportApilevelallImplementation project(":library-sync:oplus-sync-support")
    oppoFullGdprApilevelallImplementation project(":library-sync:oplus-sync-support")
    oppoLightGdprApilevelallImplementation project(":library-sync:oplus-sync-support")
    oneplusFullExportApilevelallImplementation project(":library-sync:oplus-sync-support")
    oneplusFullGdprApilevelallImplementation project(":library-sync:oplus-sync-support")

    oppoFullDomesticApilevelallImplementation project(":sdk-compat-base:sdk-compat-coloros")
    oppoLightDomesticApilevelallImplementation project(":sdk-compat-base:sdk-compat-coloros")
    oneplusFullDomesticApilevelallImplementation project(":sdk-compat-base:sdk-compat-coloros")
    oneplusLightDomesticApilevelallImplementation project(":sdk-compat-base:sdk-compat-coloros")
    oppoFullExportApilevelallImplementation project(":sdk-compat-base:sdk-compat-oplus")
    oppoLightExportApilevelallImplementation project(":sdk-compat-base:sdk-compat-oplus")
    oppoFullGdprApilevelallImplementation project(":sdk-compat-base:sdk-compat-oplus")
    oppoLightGdprApilevelallImplementation project(":sdk-compat-base:sdk-compat-oplus")
    oneplusFullExportApilevelallImplementation project(":sdk-compat-base:sdk-compat-oplus")
    oneplusFullGdprApilevelallImplementation project(":sdk-compat-base:sdk-compat-oplus")

    //gemini
    exportImplementation project(':feature:gemini')
    gdprImplementation project(':feature:gemini')

    implementation project(':feature:aigraffiti:aigraffiti-api')
    oppoFullExportApilevelallImplementation project(':feature:aigraffiti:aigraffiti-impl')
    oppoFullGdprApilevelallImplementation project(':feature:aigraffiti:aigraffiti-impl')

    implementation "org.jetbrains.kotlin:kotlin-stdlib:$kotlin_version"
    implementation "org.jetbrains.kotlinx:kotlinx-coroutines-android:$kotlinx_coroutines_version"

    implementation "androidx.core:core-ktx:${core_ktx}"
    implementation "androidx.activity:activity-ktx:$activity_ktx"
    implementation "androidx.fragment:fragment-ktx:$fragment_ktx"
    implementation "androidx.preference:preference-ktx:$preference_ktx"
    implementation "androidx.constraintlayout:constraintlayout:${constraintlayout}"
    implementation project(path: ':forcealertcomponent')


    // semantic
    implementation project(path: ':domain:semantic-component:semantic-api')
    domesticImplementation project(':domain:semantic-component:semantic-ssa')

//    implementation project(path: ':speech-wrapper')
    oppoFullDomesticApilevelallImplementation project(path: ':domain:speech:speech-breeno-service')
    oppoLightDomesticApilevelallImplementation project(path: ':domain:speech:speech-breeno-service')
    oneplusFullDomesticApilevelallImplementation project(path: ':domain:speech:speech-breeno-service')
    oneplusLightDomesticApilevelallImplementation project(path: ':domain:speech:speech-breeno-service')

    oppoFullExportApilevelallImplementation project(path: ':domain:speech:speech-azure-service')
    oppoLightExportApilevelallImplementation project(path: ':domain:speech:speech-azure-service')
    oppoFullGdprApilevelallImplementation project(path: ':domain:speech:speech-azure-service')
    oppoLightGdprApilevelallImplementation project(path: ':domain:speech:speech-azure-service')
    oneplusFullExportApilevelallImplementation project(path: ':domain:speech:speech-azure-service')
    oneplusFullGdprApilevelallImplementation project(path: ':domain:speech:speech-azure-service')

    implementation project(':domain:speech:speech-api')

    implementation project(path: ':speech-note-wrapper')
    oppoFullDomesticApilevelallImplementation project(path: ':speech-note-wrapper:note-breeno')
    oppoLightDomesticApilevelallImplementation project(path: ':speech-note-wrapper:note-breeno')
    oneplusFullDomesticApilevelallImplementation project(path: ':speech-note-wrapper:note-breeno')
    oneplusLightDomesticApilevelallImplementation project(path: ':speech-note-wrapper:note-breeno')
    oppoFullExportApilevelallImplementation project(path: ':speech-note-wrapper:note-azure')
    oppoLightExportApilevelallImplementation project(path: ':speech-note-wrapper:note-azure')
    oppoFullGdprApilevelallImplementation project(path: ':speech-note-wrapper:note-azure')
    oppoLightGdprApilevelallImplementation project(path: ':speech-note-wrapper:note-azure')
    oneplusFullExportApilevelallImplementation project(path: ':speech-note-wrapper:note-azure')
    oneplusFullGdprApilevelallImplementation project(path: ':speech-note-wrapper:note-azure')
    implementation "androidx.lifecycle:lifecycle-viewmodel-ktx:$lifecycle_version"
    implementation "androidx.lifecycle:lifecycle-livedata-ktx:$lifecycle_version"
    // legacy use for java class
    implementation "androidx.room:room-runtime:$room"
    implementation "androidx.room:room-ktx:$room"
    ksp "androidx.room:room-compiler:$room"

    //start 账号sdk  （必须引入）
    implementation "com.oplus.stdid.sdk:sdk:1.0.4"
    implementation("com.heytap.accountsdk:UCAccountSDK_Base_heytap:2.3.2.1") {
        exclude group: 'com.squareup.okhttp3', module: 'okhttp'
    }
    //end
    implementation "com.squareup.retrofit2:retrofit:$retrofit"
    implementation "com.squareup.retrofit2:converter-gson:$retrofit"

//    implementation 'com.oplusos.vfxsdk:doodleengine:3.0.10'
    implementation "com.oplus.statistics:track:${track}"

    implementation "com.google.android.material:material:$material"
    implementation "androidx.viewpager2:viewpager2:$viewpager2"

    implementation "com.oplus.materialcolor:coui-material-color:${prop_COUIMaterialColorVersion}"

    implementation 'com.oplus.sauaar:coui-sauaar:4.0.3'
    implementation 'com.facebook.rebound:rebound:0.3.8'

    implementation "com.oplusos.anim:EffectiveAnimation:${prop_EffectiveAnimation}"
    implementation 'com.ocloud.service:ocloud-sdk:1.0'
    implementation "com.squareup.okhttp3:okhttp:$okhttp"
    implementation "com.google.code.gson:gson:${gson}"

    implementation 'com.heytap.browser.libs:data_migration:1.0.4'
// 不要加这两个，会导致插件编译失败
//    compileOnly "com.oplus.sdk:addon:${prop_addonSdkVersion}@aar"
//    compileOnly "com.coloros.sdk:coloros-extension-sdk:28.2.0700.202003161125"
    implementation "com.oplus.backup:backup-sdk:2.0.0"

    // 云控SDK
    implementation 'com.oplus.nearx:cloudconfig:3.2.1'
    oppoImplementation 'com.oplus.nearx:cloudconfig-env:3.2.1'
    realmeImplementation 'com.oplus.nearx:cloudconfig-env:3.2.1'
    oneplusFullDomesticApilevelallImplementation 'com.oplus.nearx:cloudconfig-env:3.2.1'
    oneplusLightDomesticApilevelallImplementation 'com.oplus.nearx:cloudconfig-env:3.2.1'
    oneplusFullExportApilevelallImplementation 'com.oplus.nearx:cloudconfig-env-oversea:3.2.1'
    oneplusFullGdprApilevelallImplementation 'com.oplus.nearx:cloudconfig-env-oversea:3.2.1'

    //appcard
    implementation 'com.oplus.smartsdk:smartenginesdk:1.0.19'
    implementation project(':feature:card:card-api')
    implementation project(':feature:card:smart-card-impl')
    implementation project(':feature:card:note-card-impl')
    oppoFullExportApilevelallImplementation project(':feature:card:note-card-oppo-export')
    oppoLightExportApilevelallImplementation project(':feature:card:note-card-oppo-export')
    oppoFullGdprApilevelallImplementation project(':feature:card:note-card-oppo-export')
    oppoLightGdprApilevelallImplementation project(':feature:card:note-card-oppo-export')
    oneplusFullExportApilevelallImplementation project(':feature:card:note-card-oneplus-export')
    oneplusFullGdprApilevelallImplementation project(':feature:card:note-card-oneplus-export')

    //问卷 module，仅内销生效
    implementation project(':domain:questionnaire:questionnaire-api')
    domesticImplementation project(':domain:questionnaire:questionnaire-impl')

    //supertext
    domesticImplementation project(':supertext:supertext')
    exportImplementation project(':supertext:supertext-null')
    gdprImplementation project(':supertext:supertext-null')

    //export
    implementation project(':domain:export:export-api')
    implementation project(':domain:export:export-doc')

    //云控
    implementation project(':domain:cloudcontrol:cloudcontrol-api')
    implementation project(':domain:cloudcontrol:cloudcontrol-impl')

    //跳转商店
    implementation project(':domain:app-market:app-market-api')
    domesticImplementation project(':domain:app-market:app-market-domestic')
    exportImplementation project(':domain:app-market:app-market-export')

    coverageImplementation 'otestPlatform:coverageLibDisk:2.1.6@aar'
    implementation project(":feature:privacypolicy:privacypolicy-api")
    domesticImplementation project(":feature:privacypolicy:privacypolicy-impl")
    exportImplementation project(":feature:privacypolicy:exportprivacypolicy-impl")
    gdprImplementation project(":feature:privacypolicy:exportprivacypolicy-impl")

    implementation "com.oplus.aiunit.open:toolkits:$ai_sdk_version"
    implementation "com.oplus.aiunit.open:download:$ai_sdk_version"
    implementation project(':domain:wave')
    implementation project(':domain:aigc')
    implementation "com.oplus.aiunit.open:core:$ai_sdk_version"
    implementation "com.oplus.aiunit.open:av:$ai_sdk_version"
    implementation "com.oplus.aiunit.open:nlp:$ai_sdk_version"
    implementation "com.oplus.aiunit.open:note-rewrite:$ai_rewrite_sdk_version"

    // koin
    implementation "io.insert-koin:koin-android:$koin_version"

    implementation project(':domain:note-search:note-search-api')
    domesticImplementation project(':domain:note-search:note-search-dmp')
    exportImplementation project(':domain:note-search:note-search-dmp-export')
    gdprImplementation project(':domain:note-search:note-search-dmp-export')
    implementation project(':domain:note-search:note-search-local')

    implementation project(':domain:todo-search:todo-search-api')
    domesticImplementation project(':domain:todo-search:todo-search-dmp')
    exportImplementation project(':domain:todo-search:todo-search-dmp-export')
    gdprImplementation project(':domain:todo-search:todo-search-dmp-export')
    implementation project(':domain:todo-search:todo-search-local')
    //coe 动效
    implementation 'com.oplus.vfxsdk:coecommon:1.0.4-alphaa9046cc-SNAPSHOT'
    implementation 'com.oplus.vfxsdk:rsview:1.0.4-alphaa9046cc-SNAPSHOT'

    // 插件化
//    domesticCompileOnly "com.oplus.oms.split:split-core:${oms_plugin}"
//    domesticCompileOnly("com.oplus.oms.split:play-core-library:${oms_plugin}") {
//        exclude group: 'com.oplus.oms.split'
//    }
    implementation project(":domain:plugin:plugin-downloader-api")
//    domesticImplementation project(":domain:plugin:plugin-downloader-oms")
    //外销内置TblWebview
    exportImplementation project(":domain:plugin:plugin-downloader-local")
    gdprImplementation project(":domain:plugin:plugin-downloader-local")

    implementation("com.oplus.dmp.sdk:aiask-ui:$ai_ask_version")
    implementation("com.oplus.dmp.sdk:aiask:$ai_ask_version")

    implementation project(':feature:gemini:appfunctions')
    ksp project(":feature:gemini:appfunctions_codegen_plugin")

    // doc thumbnail
    kapt "com.github.bumptech.glide:compiler:$glide_version"
    implementation "com.github.bumptech.glide:annotations:$glide_version"
    implementation project(":domain:thumbnail:doc-thumbnail-api")
    implementation project(":domain:glide")
    domesticImplementation project(":domain:thumbnail:doc-thumbnail-impl")
}

apply from: rootProject.file("scripts/osigner.gradle")

kapt { correctErrorTypes = true }